import React, { useState, useEffect } from 'react'
import { Bell, AlertTriangle, Clock, Calendar, CheckCircle, X } from 'lucide-react'

const NotificationPanel = ({ appointments }) => {
  const [notifications, setNotifications] = useState([])
  const [showNotifications, setShowNotifications] = useState(false)

  useEffect(() => {
    generateNotifications()
  }, [appointments])

  const generateNotifications = () => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    const newNotifications = []

    // مواعيد اليوم
    const todayAppointments = appointments.filter(app => {
      const appDate = new Date(app.date)
      return appDate.toDateString() === today.toDateString() && app.status !== 'cancelled'
    })

    if (todayAppointments.length > 0) {
      newNotifications.push({
        id: 'today-appointments',
        type: 'info',
        title: `لديك ${todayAppointments.length} موعد اليوم`,
        message: 'تحقق من مواعيد اليوم وتأكد من جاهزيتها',
        icon: Calendar,
        color: 'blue',
        urgent: false
      })
    }

    // مواعيد غداً
    const tomorrowAppointments = appointments.filter(app => {
      const appDate = new Date(app.date)
      return appDate.toDateString() === tomorrow.toDateString() && app.status !== 'cancelled'
    })

    if (tomorrowAppointments.length > 0) {
      newNotifications.push({
        id: 'tomorrow-appointments',
        type: 'info',
        title: `لديك ${tomorrowAppointments.length} موعد غداً`,
        message: 'تحضير للمواعيد القادمة',
        icon: Clock,
        color: 'green',
        urgent: false
      })
    }

    // مواعيد في الانتظار
    const pendingAppointments = appointments.filter(app => app.status === 'pending')
    if (pendingAppointments.length > 0) {
      newNotifications.push({
        id: 'pending-appointments',
        type: 'warning',
        title: `${pendingAppointments.length} موعد في انتظار التأكيد`,
        message: 'يرجى مراجعة المواعيد وتأكيدها',
        icon: AlertTriangle,
        color: 'yellow',
        urgent: true
      })
    }

    // مواعيد متأخرة (مواعيد ماضية لم تكتمل)
    const overdueAppointments = appointments.filter(app => {
      const appDate = new Date(app.date)
      return appDate < today && (app.status === 'pending' || app.status === 'confirmed')
    })

    if (overdueAppointments.length > 0) {
      newNotifications.push({
        id: 'overdue-appointments',
        type: 'error',
        title: `${overdueAppointments.length} موعد متأخر`,
        message: 'مواعيد لم تكتمل من تواريخ سابقة',
        icon: AlertTriangle,
        color: 'red',
        urgent: true
      })
    }

    setNotifications(newNotifications)
  }

  const getNotificationColor = (color) => {
    const colors = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      red: 'bg-red-50 border-red-200 text-red-800'
    }
    return colors[color] || colors.blue
  }

  const urgentNotifications = notifications.filter(n => n.urgent)
  const regularNotifications = notifications.filter(n => !n.urgent)

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative p-3 bg-white rounded-full shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300"
      >
        <Bell className="w-6 h-6 text-gray-600" />
        {notifications.length > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
            {notifications.length}
          </span>
        )}
        {urgentNotifications.length > 0 && (
          <span className="absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
        )}
      </button>

      {/* Notifications Dropdown */}
      {showNotifications && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 max-h-96 overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800">الإشعارات</h3>
              <button
                onClick={() => setShowNotifications(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="max-h-80 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-6 text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
                <p className="text-gray-600">لا توجد إشعارات جديدة</p>
                <p className="text-sm text-gray-500">جميع المواعيد منظمة بشكل جيد</p>
              </div>
            ) : (
              <div className="p-2">
                {/* Urgent Notifications */}
                {urgentNotifications.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-red-600 px-2 mb-2">عاجل</h4>
                    {urgentNotifications.map((notification) => (
                      <NotificationItem key={notification.id} notification={notification} />
                    ))}
                  </div>
                )}

                {/* Regular Notifications */}
                {regularNotifications.length > 0 && (
                  <div>
                    {urgentNotifications.length > 0 && (
                      <h4 className="text-sm font-semibold text-gray-600 px-2 mb-2">عام</h4>
                    )}
                    {regularNotifications.map((notification) => (
                      <NotificationItem key={notification.id} notification={notification} />
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

const NotificationItem = ({ notification }) => {
  const { icon: Icon, title, message, color, urgent } = notification

  const getNotificationColor = (color) => {
    const colors = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      red: 'bg-red-50 border-red-200 text-red-800'
    }
    return colors[color] || colors.blue
  }

  return (
    <div className={`p-3 m-2 rounded-lg border-2 ${getNotificationColor(color)} ${urgent ? 'ring-2 ring-red-300' : ''}`}>
      <div className="flex items-start space-x-3 space-x-reverse">
        <div className={`p-2 rounded-lg bg-white shadow-sm`}>
          <Icon className="w-4 h-4" />
        </div>
        <div className="flex-1">
          <h5 className="font-semibold text-sm mb-1">{title}</h5>
          <p className="text-xs opacity-80">{message}</p>
        </div>
        {urgent && (
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
        )}
      </div>
    </div>
  )
}

export default NotificationPanel
