# نظام حجز المواعيد الطبية

واجهة ويب تفاعلية وحديثة لحجز المواعيد الطبية باستخدام React.js و TailwindCSS مع خادم Node.js.

## 🌟 المميزات

- **واجهة مستخدم حديثة**: تصميم جذاب ومتجاوب باستخدام TailwindCSS
- **نموذج تفاعلي**: تحقق فوري من البيانات مع رسائل خطأ واضحة
- **رفع الملفات**: إمكانية رفع التحاليل والصور الطبية (PDF, JPG, PNG)
- **تجربة مستخدم ممتازة**: رسائل نجاح وتحميل تفاعلية
- **أمان عالي**: حماية من الملفات الضارة وتحديد حجم الملفات
- **قاعدة بيانات**: حفظ المواعيد في قاعدة بيانات SQLite
- **لوحة إدارة متقدمة**: إدارة شاملة للمواعيد مع إحصائيات وتقويم
- **نظام البحث والفلترة**: بحث متقدم وفلترة حسب معايير متعددة
- **نظام الإشعارات**: تنبيهات ذكية للمواعيد المهمة
- **تصدير البيانات**: إمكانية تصدير المواعيد إلى ملف CSV

## 🛠️ التقنيات المستخدمة

### Frontend
- React.js 18
- TailwindCSS
- Lucide React (الأيقونات)
- React Hot Toast (الإشعارات)
- Vite (أداة البناء)

### Backend
- Node.js
- Express.js
- SQLite3
- Multer (رفع الملفات)
- Helmet (الأمان)
- CORS

## 📋 المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات للواجهة الأمامية

```bash
npm install
```

### 2. تثبيت المتطلبات للخادم

```bash
cd server
npm install
```

### 3. تشغيل الخادم

```bash
cd server
npm start
```

الخادم سيعمل على: `http://localhost:5000`

### 4. تشغيل الواجهة الأمامية

في نافذة طرفية جديدة:

```bash
npm run dev
```

الواجهة ستعمل على: `http://localhost:3000`

### 5. الوصول للوحة الإدارة

لعرض المواعيد المحجوزة، افتح: `http://localhost:3000/admin.html`

## 🎛️ مميزات لوحة الإدارة المتقدمة

### 📊 الإحصائيات والتحليلات
- **إحصائيات شاملة**: عرض إجمالي المواعيد، المؤكدة، المكتملة، الملغية
- **مواعيد اليوم**: عدد المواعيد المجدولة لليوم الحالي
- **تصنيف حسب النوع**: إحصائيات الفحوصات الطبية والعمليات الجراحية
- **رسوم بيانية تفاعلية**: عرض مرئي للبيانات

### 🔍 البحث والفلترة المتقدمة
- **البحث النصي**: بحث بالاسم أو رقم الهاتف
- **فلترة حسب الحالة**: عرض المواعيد حسب حالتها (في الانتظار، مؤكدة، مكتملة، ملغية)
- **فلترة حسب التاريخ**: اليوم، غداً، هذا الأسبوع، هذا الشهر
- **نتائج فورية**: تحديث النتائج أثناء الكتابة

### 🔔 نظام الإشعارات الذكية
- **تنبيهات عاجلة**: للمواعيد المتأخرة والمواعيد في الانتظار
- **مواعيد اليوم**: تذكير بمواعيد اليوم الحالي
- **مواعيد الغد**: تنبيه للمواعيد القادمة
- **عداد الإشعارات**: عرض عدد الإشعارات غير المقروءة

### 📅 عرض التقويم الشهري
- **تقويم تفاعلي**: عرض المواعيد على التقويم الشهري
- **ألوان مميزة**: تمييز المواعيد حسب حالتها
- **تفاصيل سريعة**: عرض تفاصيل المواعيد عند النقر على التاريخ
- **تنقل سهل**: التنقل بين الشهور بسهولة

### ⚡ إدارة المواعيد
- **تغيير الحالة**: تأكيد، إكمال، أو إلغاء المواعيد
- **عرض التفاصيل**: نافذة مفصلة لكل موعد
- **اتصال مباشر**: أزرار للاتصال بالمرضى
- **حذف المواعيد**: إمكانية حذف المواعيد غير المرغوبة

### 📤 تصدير البيانات
- **تصدير CSV**: تصدير جميع المواعيد إلى ملف Excel
- **تصدير مفلتر**: تصدير النتائج المفلترة فقط
- **تنسيق عربي**: دعم كامل للغة العربية في الملفات المصدرة

## 📁 هيكل المشروع

```
medical-appointment-app/
├── src/
│   ├── components/
│   │   ├── AppointmentForm.jsx    # نموذج حجز الموعد الرئيسي
│   │   ├── FormField.jsx          # مكون حقول النموذج
│   │   ├── FileUpload.jsx         # مكون رفع الملفات
│   │   ├── Header.jsx             # رأس الصفحة
│   │   └── SuccessModal.jsx       # نافذة النجاح
│   ├── App.jsx                    # المكون الرئيسي
│   ├── main.jsx                   # نقطة دخول التطبيق
│   └── index.css                  # الأنماط الأساسية
├── server/
│   ├── server.js                  # خادم Node.js
│   ├── package.json               # متطلبات الخادم
│   └── uploads/                   # مجلد الملفات المرفوعة
├── package.json                   # متطلبات الواجهة الأمامية
├── tailwind.config.js             # إعدادات TailwindCSS
└── vite.config.js                 # إعدادات Vite
```

## 🔧 الإعدادات

### إعدادات الملفات
- الحد الأقصى لحجم الملف: 5 ميجابايت
- الحد الأقصى لعدد الملفات: 5 ملفات
- الأنواع المدعومة: PDF, JPG, PNG

### إعدادات قاعدة البيانات
- نوع قاعدة البيانات: SQLite
- ملف قاعدة البيانات: `appointments.db`
- الجداول: `appointments`, `appointment_files`

## 🔒 الأمان

- تحقق من نوع الملفات المرفوعة
- تحديد حجم الملفات
- حماية من هجمات CSRF
- تحديد معدل الطلبات (Rate Limiting)
- تشفير رؤوس HTTP باستخدام Helmet

## 📱 التجاوب

التطبيق متجاوب بالكامل ويعمل على:
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية

## 🎨 التصميم

- ألوان طبية هادئة (أزرق، أبيض، رمادي)
- خطوط عربية واضحة (Cairo, Tajawal)
- تأثيرات تفاعلية ناعمة
- أيقونات واضحة ومعبرة

## 🔄 API Endpoints

### POST `/api/appointments`
إنشاء موعد جديد

**البيانات المطلوبة:**
- `fullName`: الاسم الكامل
- `phoneNumber`: رقم الهاتف
- `appointmentType`: نوع الموعد
- `date`: التاريخ
- `time`: الوقت
- `files`: الملفات (اختياري)

### GET `/api/appointments`
جلب جميع المواعيد

### GET `/api/health`
فحص حالة الخادم

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بالخادم**
   - تأكد من تشغيل الخادم على المنفذ 5000
   - تحقق من إعدادات CORS

2. **مشاكل رفع الملفات**
   - تأكد من نوع الملف المدعوم
   - تحقق من حجم الملف (أقل من 5 ميجابايت)

3. **مشاكل قاعدة البيانات**
   - تأكد من وجود صلاحيات الكتابة في مجلد المشروع

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
