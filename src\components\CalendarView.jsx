import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Calendar, Clock, User } from 'lucide-react'

const CalendarView = ({ appointments, onDateSelect }) => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState(null)

  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ]

  const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']

  const getDaysInMonth = (date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const getAppointmentsForDate = (date) => {
    if (!date) return []
    
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date)
      return appointmentDate.toDateString() === date.toDateString()
    })
  }

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate)
    newDate.setMonth(newDate.getMonth() + direction)
    setCurrentDate(newDate)
  }

  const isToday = (date) => {
    if (!date) return false
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isSelected = (date) => {
    if (!date || !selectedDate) return false
    return date.toDateString() === selectedDate.toDateString()
  }

  const handleDateClick = (date) => {
    if (!date) return
    setSelectedDate(date)
    if (onDateSelect) {
      onDateSelect(date)
    }
  }

  const days = getDaysInMonth(currentDate)

  return (
    <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigateMonth(-1)}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
        
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-800">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h3>
        </div>
        
        <button
          onClick={() => navigateMonth(1)}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <ChevronLeft className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Day Names */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day) => (
          <div key={day} className="p-2 text-center text-sm font-semibold text-gray-600">
            {day.substring(0, 3)}
          </div>
        ))}
      </div>

      {/* Calendar Days */}
      <div className="grid grid-cols-7 gap-1">
        {days.map((date, index) => {
          const dayAppointments = getAppointmentsForDate(date)
          const hasAppointments = dayAppointments.length > 0
          
          return (
            <div
              key={index}
              onClick={() => handleDateClick(date)}
              className={`
                relative p-2 min-h-[60px] border border-gray-100 rounded-lg cursor-pointer transition-all duration-200
                ${date ? 'hover:bg-gray-50' : ''}
                ${isToday(date) ? 'bg-blue-100 border-blue-300' : ''}
                ${isSelected(date) ? 'bg-medical-100 border-medical-300' : ''}
                ${!date ? 'cursor-default' : ''}
              `}
            >
              {date && (
                <>
                  <div className={`text-sm font-medium ${
                    isToday(date) ? 'text-blue-700' : 
                    isSelected(date) ? 'text-medical-700' : 'text-gray-700'
                  }`}>
                    {date.getDate()}
                  </div>
                  
                  {hasAppointments && (
                    <div className="mt-1 space-y-1">
                      {dayAppointments.slice(0, 2).map((appointment, idx) => (
                        <div
                          key={idx}
                          className={`text-xs p-1 rounded text-white truncate ${
                            appointment.status === 'pending' ? 'bg-yellow-500' :
                            appointment.status === 'confirmed' ? 'bg-blue-500' :
                            appointment.status === 'completed' ? 'bg-green-500' :
                            'bg-red-500'
                          }`}
                        >
                          {appointment.time} - {appointment.full_name.split(' ')[0]}
                        </div>
                      ))}
                      
                      {dayAppointments.length > 2 && (
                        <div className="text-xs text-gray-500 text-center">
                          +{dayAppointments.length - 2} أخرى
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          )
        })}
      </div>

      {/* Selected Date Details */}
      {selectedDate && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-800 mb-3 flex items-center space-x-2 space-x-reverse">
            <Calendar className="w-5 h-5 text-medical-500" />
            <span>
              {selectedDate.toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </h4>
          
          {getAppointmentsForDate(selectedDate).length === 0 ? (
            <p className="text-gray-500 text-sm">لا توجد مواعيد في هذا اليوم</p>
          ) : (
            <div className="space-y-2">
              {getAppointmentsForDate(selectedDate).map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${
                      appointment.status === 'pending' ? 'bg-yellow-500' :
                      appointment.status === 'confirmed' ? 'bg-blue-500' :
                      appointment.status === 'completed' ? 'bg-green-500' :
                      'bg-red-500'
                    }`}></div>
                    <div>
                      <p className="font-medium text-gray-800">{appointment.full_name}</p>
                      <p className="text-sm text-gray-600 flex items-center space-x-1 space-x-reverse">
                        <Clock className="w-4 h-4" />
                        <span>{appointment.time}</span>
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{appointment.phone_number}</p>
                    <p className="text-xs text-gray-500">
                      {appointment.appointment_type === 'medical-checkup' ? 'فحص طبي' : 'عملية جراحية'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default CalendarView
