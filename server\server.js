import express from 'express'
import cors from 'cors'
import multer from 'multer'
import sqlite3 from 'sqlite3'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'
import fs from 'fs'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'

const app = express()
const PORT = process.env.PORT || 5000

// Security middleware
app.use(helmet())

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
})
app.use(limiter)

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}))

app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// Create uploads directory if it doesn't exist
const uploadsDir = './uploads'
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true })
}

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('Invalid file type. Only PDF, JPG, and PNG files are allowed.'), false)
  }
}

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5 // Maximum 5 files
  }
})

// Initialize SQLite database
const db = new sqlite3.Database('./appointments.db', (err) => {
  if (err) {
    console.error('Error opening database:', err.message)
  } else {
    console.log('Connected to SQLite database.')
    
    // Create appointments table
    db.run(`
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        full_name TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        appointment_type TEXT NOT NULL,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        files TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'pending'
      )
    `)
    
    // Create files table
    db.run(`
      CREATE TABLE IF NOT EXISTS appointment_files (
        id TEXT PRIMARY KEY,
        appointment_id TEXT NOT NULL,
        original_name TEXT NOT NULL,
        filename TEXT NOT NULL,
        mimetype TEXT NOT NULL,
        size INTEGER NOT NULL,
        FOREIGN KEY (appointment_id) REFERENCES appointments (id)
      )
    `)
  }
})

// Validation functions
const validatePhoneNumber = (phone) => {
  const phoneRegex = /^[0-9+\-\s()]{10,15}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

const validateDate = (dateString) => {
  const date = new Date(dateString)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return date >= today
}

// API Routes
app.post('/api/appointments', upload.array('files', 5), (req, res) => {
  try {
    const { fullName, phoneNumber, appointmentType, date, time } = req.body
    
    // Validation
    if (!fullName || !phoneNumber || !appointmentType || !date || !time) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      })
    }
    
    if (!validatePhoneNumber(phoneNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      })
    }
    
    if (!validateDate(date)) {
      return res.status(400).json({
        success: false,
        message: 'Date cannot be in the past'
      })
    }
    
    const appointmentId = uuidv4()
    
    // Insert appointment
    db.run(
      `INSERT INTO appointments (id, full_name, phone_number, appointment_type, date, time)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [appointmentId, fullName, phoneNumber, appointmentType, date, time],
      function(err) {
        if (err) {
          console.error('Database error:', err)
          return res.status(500).json({
            success: false,
            message: 'Error saving appointment'
          })
        }
        
        // Save file information if files were uploaded
        if (req.files && req.files.length > 0) {
          const filePromises = req.files.map(file => {
            return new Promise((resolve, reject) => {
              const fileId = uuidv4()
              db.run(
                `INSERT INTO appointment_files (id, appointment_id, original_name, filename, mimetype, size)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [fileId, appointmentId, file.originalname, file.filename, file.mimetype, file.size],
                (err) => {
                  if (err) reject(err)
                  else resolve()
                }
              )
            })
          })
          
          Promise.all(filePromises)
            .then(() => {
              res.json({
                success: true,
                message: 'Appointment booked successfully',
                appointmentId: appointmentId,
                filesUploaded: req.files.length
              })
            })
            .catch(err => {
              console.error('Error saving files:', err)
              res.status(500).json({
                success: false,
                message: 'Appointment saved but error uploading files'
              })
            })
        } else {
          res.json({
            success: true,
            message: 'Appointment booked successfully',
            appointmentId: appointmentId
          })
        }
      }
    )
    
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

// Get all appointments (for admin panel)
app.get('/api/appointments', (req, res) => {
  db.all(
    `SELECT a.*, 
     GROUP_CONCAT(af.original_name) as file_names
     FROM appointments a
     LEFT JOIN appointment_files af ON a.id = af.appointment_id
     GROUP BY a.id
     ORDER BY a.created_at DESC`,
    (err, rows) => {
      if (err) {
        console.error('Database error:', err)
        return res.status(500).json({
          success: false,
          message: 'Error fetching appointments'
        })
      }
      
      res.json({
        success: true,
        appointments: rows
      })
    }
  )
})

// Update appointment status
app.put('/api/appointments/:id/status', (req, res) => {
  try {
    const { id } = req.params
    const { status } = req.body

    const validStatuses = ['pending', 'confirmed', 'completed', 'cancelled']
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      })
    }

    db.run(
      'UPDATE appointments SET status = ? WHERE id = ?',
      [status, id],
      function(err) {
        if (err) {
          console.error('Database error:', err)
          return res.status(500).json({
            success: false,
            message: 'Error updating appointment status'
          })
        }

        if (this.changes === 0) {
          return res.status(404).json({
            success: false,
            message: 'Appointment not found'
          })
        }

        res.json({
          success: true,
          message: 'Appointment status updated successfully'
        })
      }
    )
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

// Delete appointment
app.delete('/api/appointments/:id', (req, res) => {
  try {
    const { id } = req.params

    // First delete associated files
    db.run(
      'DELETE FROM appointment_files WHERE appointment_id = ?',
      [id],
      (err) => {
        if (err) {
          console.error('Error deleting files:', err)
        }

        // Then delete the appointment
        db.run(
          'DELETE FROM appointments WHERE id = ?',
          [id],
          function(err) {
            if (err) {
              console.error('Database error:', err)
              return res.status(500).json({
                success: false,
                message: 'Error deleting appointment'
              })
            }

            if (this.changes === 0) {
              return res.status(404).json({
                success: false,
                message: 'Appointment not found'
              })
            }

            res.json({
              success: true,
              message: 'Appointment deleted successfully'
            })
          }
        )
      }
    )
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

// Get appointment statistics
app.get('/api/appointments/stats', (req, res) => {
  try {
    const queries = [
      'SELECT COUNT(*) as total FROM appointments',
      'SELECT COUNT(*) as pending FROM appointments WHERE status = "pending"',
      'SELECT COUNT(*) as confirmed FROM appointments WHERE status = "confirmed"',
      'SELECT COUNT(*) as completed FROM appointments WHERE status = "completed"',
      'SELECT COUNT(*) as cancelled FROM appointments WHERE status = "cancelled"',
      'SELECT COUNT(*) as today FROM appointments WHERE date = date("now")',
      'SELECT COUNT(*) as medical_checkups FROM appointments WHERE appointment_type = "medical-checkup"',
      'SELECT COUNT(*) as surgeries FROM appointments WHERE appointment_type = "surgery"'
    ]

    Promise.all(queries.map(query =>
      new Promise((resolve, reject) => {
        db.get(query, (err, row) => {
          if (err) reject(err)
          else resolve(row)
        })
      })
    )).then(results => {
      const stats = {
        total: results[0].total,
        pending: results[1].pending,
        confirmed: results[2].confirmed,
        completed: results[3].completed,
        cancelled: results[4].cancelled,
        today: results[5].today,
        medicalCheckups: results[6].medical_checkups,
        surgeries: results[7].surgeries
      }

      res.json({
        success: true,
        stats: stats
      })
    }).catch(err => {
      console.error('Database error:', err)
      res.status(500).json({
        success: false,
        message: 'Error fetching statistics'
      })
    })
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() })
})

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 5MB.'
      })
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 5 files.'
      })
    }
  }
  
  res.status(500).json({
    success: false,
    message: error.message || 'Internal server error'
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
  console.log(`Health check: http://localhost:${PORT}/api/health`)
})
