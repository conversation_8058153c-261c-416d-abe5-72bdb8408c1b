import React from 'react'
import { 
  User, Phone, Calendar, Clock, FileText, Eye, CheckCircle, 
  XCircle, AlertCircle, Trash2 
} from 'lucide-react'

const AppointmentCard = ({ 
  appointment, 
  onViewDetails, 
  onUpdateStatus, 
  onDelete,
  appointmentTypeLabels 
}) => {
  const getStatusLabel = (status) => {
    const statusLabels = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'completed': 'مكتمل',
      'cancelled': 'ملغي'
    }
    return statusLabels[status] || status
  }

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'confirmed': 'bg-blue-100 text-blue-800 border-blue-200',
      'completed': 'bg-green-100 text-green-800 border-green-200',
      'cancelled': 'bg-red-100 text-red-800 border-red-200'
    }
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getStatusIcon = (status) => {
    const icons = {
      'pending': <AlertCircle className="w-4 h-4" />,
      'confirmed': <CheckCircle className="w-4 h-4" />,
      'completed': <CheckCircle className="w-4 h-4" />,
      'cancelled': <XCircle className="w-4 h-4" />
    }
    return icons[status] || <AlertCircle className="w-4 h-4" />
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString('ar-SA')
  }

  const isToday = (dateString) => {
    const today = new Date()
    const appointmentDate = new Date(dateString)
    return appointmentDate.toDateString() === today.toDateString()
  }

  const isTomorrow = (dateString) => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const appointmentDate = new Date(dateString)
    return appointmentDate.toDateString() === tomorrow.toDateString()
  }

  const getDateLabel = (dateString) => {
    if (isToday(dateString)) return 'اليوم'
    if (isTomorrow(dateString)) return 'غداً'
    return formatDate(dateString)
  }

  return (
    <div className={`bg-white rounded-xl p-6 border-2 transition-all duration-300 hover:shadow-lg ${
      isToday(appointment.date) ? 'border-blue-300 bg-blue-50' : 
      isTomorrow(appointment.date) ? 'border-green-300 bg-green-50' : 
      'border-gray-200 hover:border-medical-300'
    }`}>
      {/* Header with Status and Date */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className={`px-3 py-1 rounded-full text-sm font-medium border flex items-center space-x-1 space-x-reverse ${getStatusColor(appointment.status)}`}>
            {getStatusIcon(appointment.status)}
            <span>{getStatusLabel(appointment.status)}</span>
          </span>
          
          {(isToday(appointment.date) || isTomorrow(appointment.date)) && (
            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
              isToday(appointment.date) ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'
            }`}>
              {getDateLabel(appointment.date)}
            </span>
          )}
        </div>

        <div className="text-sm text-gray-500">
          {formatDateTime(appointment.created_at)}
        </div>
      </div>

      {/* Patient Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="bg-medical-100 p-2 rounded-lg">
              <User className="w-4 h-4 text-medical-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">اسم المريض</p>
              <p className="font-semibold text-gray-800">{appointment.full_name}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="bg-medical-100 p-2 rounded-lg">
              <Phone className="w-4 h-4 text-medical-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">رقم الهاتف</p>
              <p className="font-semibold text-gray-800">{appointment.phone_number}</p>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="bg-medical-100 p-2 rounded-lg">
              <FileText className="w-4 h-4 text-medical-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">نوع الموعد</p>
              <p className="font-semibold text-gray-800">
                {appointmentTypeLabels[appointment.appointment_type]}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="bg-medical-100 p-2 rounded-lg">
              <Calendar className="w-4 h-4 text-medical-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">التاريخ والوقت</p>
              <p className="font-semibold text-gray-800">
                {getDateLabel(appointment.date)} - {appointment.time}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Files */}
      {appointment.file_names && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-xs text-gray-500 mb-2">الملفات المرفقة:</p>
          <div className="flex flex-wrap gap-1">
            {appointment.file_names.split(',').map((fileName, index) => (
              <span
                key={index}
                className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
              >
                {fileName.length > 20 ? fileName.substring(0, 20) + '...' : fileName}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
        <button
          onClick={() => onViewDetails(appointment)}
          className="bg-blue-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse flex-1 justify-center"
        >
          <Eye className="w-4 h-4" />
          <span>عرض التفاصيل</span>
        </button>

        {appointment.status === 'pending' && (
          <button
            onClick={() => onUpdateStatus(appointment.id, 'confirmed')}
            className="bg-green-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse"
          >
            <CheckCircle className="w-4 h-4" />
            <span>تأكيد</span>
          </button>
        )}

        {appointment.status === 'confirmed' && (
          <button
            onClick={() => onUpdateStatus(appointment.id, 'completed')}
            className="bg-purple-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse"
          >
            <CheckCircle className="w-4 h-4" />
            <span>إكمال</span>
          </button>
        )}

        {(appointment.status === 'pending' || appointment.status === 'confirmed') && (
          <button
            onClick={() => onUpdateStatus(appointment.id, 'cancelled')}
            className="bg-red-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse"
          >
            <XCircle className="w-4 h-4" />
            <span>إلغاء</span>
          </button>
        )}

        <button
          onClick={() => window.open(`tel:${appointment.phone_number}`, '_self')}
          className="bg-indigo-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse"
        >
          <Phone className="w-4 h-4" />
          <span>اتصال</span>
        </button>

        <button
          onClick={() => onDelete(appointment.id)}
          className="bg-gray-500 text-white px-3 py-2 rounded-lg text-sm btn-hover flex items-center space-x-1 space-x-reverse"
        >
          <Trash2 className="w-4 h-4" />
          <span>حذف</span>
        </button>
      </div>
    </div>
  )
}

export default AppointmentCard
