import React, { useState, useEffect } from 'react'
import {
  Calendar, Phone, User, FileText, Clock, RefreshCw, Search, Filter,
  Download, BarChart3, CheckCircle, XCircle, AlertCircle, Edit3,
  Trash2, Eye, Mail, MessageSquare, TrendingUp, Users, CalendarDays
} from 'lucide-react'
import toast from 'react-hot-toast'
import AppointmentCard from './AppointmentCard'
import StatsCard from './StatsCard'
import NotificationPanel from './NotificationPanel'
import CalendarView from './CalendarView'

const AdminPanel = () => {
  const [appointments, setAppointments] = useState([])
  const [filteredAppointments, setFilteredAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [showStats, setShowStats] = useState(false)
  const [selectedAppointment, setSelectedAppointment] = useState(null)
  const [showDetails, setShowDetails] = useState(false)
  const [showCalendar, setShowCalendar] = useState(false)
  const [viewMode, setViewMode] = useState('list') // 'list' or 'calendar'

  const appointmentTypeLabels = {
    'medical-checkup': 'فحص طبي',
    'surgery': 'عملية جراحية'
  }

  const fetchAppointments = async () => {
    try {
      setLoading(true)
      const response = await fetch('http://localhost:5000/api/appointments')
      const result = await response.json()

      if (result.success) {
        setAppointments(result.appointments)
        setFilteredAppointments(result.appointments)
      } else {
        toast.error('فشل في جلب المواعيد')
      }
    } catch (error) {
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  const updateAppointmentStatus = async (appointmentId, newStatus) => {
    try {
      const response = await fetch(`http://localhost:5000/api/appointments/${appointmentId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم تحديث حالة الموعد بنجاح')
        fetchAppointments()
      } else {
        toast.error('فشل في تحديث حالة الموعد')
      }
    } catch (error) {
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  const deleteAppointment = async (appointmentId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:5000/api/appointments/${appointmentId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم حذف الموعد بنجاح')
        fetchAppointments()
      } else {
        toast.error('فشل في حذف الموعد')
      }
    } catch (error) {
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  useEffect(() => {
    fetchAppointments()
  }, [])

  useEffect(() => {
    filterAppointments()
  }, [appointments, searchTerm, statusFilter, dateFilter])

  const filterAppointments = () => {
    let filtered = [...appointments]

    // البحث بالاسم أو رقم الهاتف
    if (searchTerm) {
      filtered = filtered.filter(appointment =>
        appointment.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.phone_number.includes(searchTerm)
      )
    }

    // فلترة حسب الحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter)
    }

    // فلترة حسب التاريخ
    if (dateFilter !== 'all') {
      const today = new Date()
      const appointmentDate = new Date()

      switch (dateFilter) {
        case 'today':
          filtered = filtered.filter(appointment => {
            const appDate = new Date(appointment.date)
            return appDate.toDateString() === today.toDateString()
          })
          break
        case 'tomorrow':
          const tomorrow = new Date(today)
          tomorrow.setDate(tomorrow.getDate() + 1)
          filtered = filtered.filter(appointment => {
            const appDate = new Date(appointment.date)
            return appDate.toDateString() === tomorrow.toDateString()
          })
          break
        case 'week':
          const weekFromNow = new Date(today)
          weekFromNow.setDate(weekFromNow.getDate() + 7)
          filtered = filtered.filter(appointment => {
            const appDate = new Date(appointment.date)
            return appDate >= today && appDate <= weekFromNow
          })
          break
        case 'month':
          const monthFromNow = new Date(today)
          monthFromNow.setMonth(monthFromNow.getMonth() + 1)
          filtered = filtered.filter(appointment => {
            const appDate = new Date(appointment.date)
            return appDate >= today && appDate <= monthFromNow
          })
          break
      }
    }

    setFilteredAppointments(filtered)
  }

  const getStats = () => {
    const total = appointments.length
    const pending = appointments.filter(app => app.status === 'pending').length
    const confirmed = appointments.filter(app => app.status === 'confirmed').length
    const completed = appointments.filter(app => app.status === 'completed').length
    const cancelled = appointments.filter(app => app.status === 'cancelled').length

    const today = new Date()
    const todayAppointments = appointments.filter(app => {
      const appDate = new Date(app.date)
      return appDate.toDateString() === today.toDateString()
    }).length

    const medicalCheckups = appointments.filter(app => app.appointment_type === 'medical-checkup').length
    const surgeries = appointments.filter(app => app.appointment_type === 'surgery').length

    return {
      total, pending, confirmed, completed, cancelled,
      todayAppointments, medicalCheckups, surgeries
    }
  }

  const exportToCSV = () => {
    const csvContent = [
      ['الاسم', 'رقم الهاتف', 'نوع الموعد', 'التاريخ', 'الوقت', 'الحالة', 'تاريخ الحجز'],
      ...filteredAppointments.map(app => [
        app.full_name,
        app.phone_number,
        appointmentTypeLabels[app.appointment_type],
        app.date,
        app.time,
        getStatusLabel(app.status),
        new Date(app.created_at).toLocaleDateString('ar-SA')
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `appointments_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    toast.success('تم تصدير البيانات بنجاح')
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString('ar-SA')
  }

  const getStatusLabel = (status) => {
    const statusLabels = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'completed': 'مكتمل',
      'cancelled': 'ملغي'
    }
    return statusLabels[status] || status
  }

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'confirmed': 'bg-blue-100 text-blue-800',
      'completed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const getStatusIcon = (status) => {
    const icons = {
      'pending': <AlertCircle className="w-4 h-4" />,
      'confirmed': <CheckCircle className="w-4 h-4" />,
      'completed': <CheckCircle className="w-4 h-4" />,
      'cancelled': <XCircle className="w-4 h-4" />
    }
    return icons[status] || <AlertCircle className="w-4 h-4" />
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-medical-50 to-medical-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-medical-500 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المواعيد...</p>
        </div>
      </div>
    )
  }

  const stats = getStats()

  return (
    <div className="min-h-screen bg-gradient-to-br from-medical-50 to-medical-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
            <div className="flex items-center space-x-4 space-x-reverse mb-4 lg:mb-0">
              <div className="bg-medical-500 p-3 rounded-full">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">لوحة إدارة المواعيد</h1>
                <p className="text-gray-600">إجمالي المواعيد: {appointments.length} | المعروضة: {filteredAppointments.length}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-3 items-center">
              <NotificationPanel appointments={appointments} />

              <button
                onClick={() => setViewMode(viewMode === 'list' ? 'calendar' : 'list')}
                className="bg-purple-500 text-white px-4 py-2 rounded-lg font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
              >
                <Calendar className="w-5 h-5" />
                <span>{viewMode === 'list' ? 'التقويم' : 'القائمة'}</span>
              </button>

              <button
                onClick={() => setShowStats(!showStats)}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
              >
                <BarChart3 className="w-5 h-5" />
                <span>الإحصائيات</span>
              </button>

              <button
                onClick={exportToCSV}
                className="bg-green-500 text-white px-4 py-2 rounded-lg font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
              >
                <Download className="w-5 h-5" />
                <span>تصدير</span>
              </button>

              <button
                onClick={fetchAppointments}
                className="bg-medical-500 text-white px-4 py-2 rounded-lg font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
              >
                <RefreshCw className="w-5 h-5" />
                <span>تحديث</span>
              </button>
            </div>
          </div>

          {/* Statistics Cards */}
          {showStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
              <StatsCard
                icon={Users}
                title="إجمالي المواعيد"
                value={stats.total}
                color="blue"
              />

              <StatsCard
                icon={AlertCircle}
                title="في الانتظار"
                value={stats.pending}
                color="yellow"
              />

              <StatsCard
                icon={CheckCircle}
                title="مؤكدة"
                value={stats.confirmed}
                color="green"
              />

              <StatsCard
                icon={CheckCircle}
                title="مكتملة"
                value={stats.completed}
                color="purple"
              />

              <StatsCard
                icon={XCircle}
                title="ملغية"
                value={stats.cancelled}
                color="red"
              />

              <StatsCard
                icon={CalendarDays}
                title="اليوم"
                value={stats.todayAppointments}
                color="indigo"
              />

              <StatsCard
                icon={FileText}
                title="فحص طبي"
                value={stats.medicalCheckups}
                color="teal"
              />

              <StatsCard
                icon={FileText}
                title="عمليات"
                value={stats.surgeries}
                color="orange"
              />
            </div>
          )}

          {/* Search and Filter Controls */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث بالاسم أو رقم الهاتف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-transparent"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-transparent"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="confirmed">مؤكدة</option>
              <option value="completed">مكتملة</option>
              <option value="cancelled">ملغية</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-transparent"
            >
              <option value="all">جميع التواريخ</option>
              <option value="today">اليوم</option>
              <option value="tomorrow">غداً</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
            </select>
          </div>
        </div>

        {/* Content Area */}
        {viewMode === 'calendar' ? (
          <CalendarView
            appointments={filteredAppointments}
            onDateSelect={(date) => {
              // Filter appointments for selected date
              const dateString = date.toISOString().split('T')[0]
              setDateFilter('all')
              setSearchTerm('')
              // You could add more specific date filtering here
            }}
          />
        ) : (
          <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
            {filteredAppointments.length === 0 ? (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  {appointments.length === 0 ? 'لا توجد مواعيد' : 'لا توجد نتائج'}
                </h3>
                <p className="text-gray-500">
                  {appointments.length === 0 ? 'لم يتم حجز أي مواعيد بعد' : 'جرب تغيير معايير البحث أو الفلترة'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredAppointments.map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                    appointmentTypeLabels={appointmentTypeLabels}
                    onViewDetails={(appointment) => {
                      setSelectedAppointment(appointment)
                      setShowDetails(true)
                    }}
                    onUpdateStatus={updateAppointmentStatus}
                    onDelete={deleteAppointment}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Appointment Details Modal */}
      {showDetails && selectedAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full p-8 relative max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setShowDetails(false)}
              className="absolute top-4 left-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XCircle className="w-6 h-6" />
            </button>

            <div className="text-center mb-6">
              <div className="bg-medical-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <User className="w-10 h-10 text-medical-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">تفاصيل الموعد</h3>
              <span className={`px-4 py-2 rounded-full text-sm font-medium flex items-center justify-center space-x-2 space-x-reverse w-fit mx-auto ${getStatusColor(selectedAppointment.status)}`}>
                {getStatusIcon(selectedAppointment.status)}
                <span>{getStatusLabel(selectedAppointment.status)}</span>
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <User className="w-5 h-5 text-medical-500" />
                  <div>
                    <p className="text-sm text-gray-500">اسم المريض</p>
                    <p className="font-semibold text-gray-800">{selectedAppointment.full_name}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 space-x-reverse">
                  <Phone className="w-5 h-5 text-medical-500" />
                  <div>
                    <p className="text-sm text-gray-500">رقم الهاتف</p>
                    <p className="font-semibold text-gray-800">{selectedAppointment.phone_number}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 space-x-reverse">
                  <FileText className="w-5 h-5 text-medical-500" />
                  <div>
                    <p className="text-sm text-gray-500">نوع الموعد</p>
                    <p className="font-semibold text-gray-800">
                      {appointmentTypeLabels[selectedAppointment.appointment_type]}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Calendar className="w-5 h-5 text-medical-500" />
                  <div>
                    <p className="text-sm text-gray-500">تاريخ الموعد</p>
                    <p className="font-semibold text-gray-800">
                      {formatDate(selectedAppointment.date)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 space-x-reverse">
                  <Clock className="w-5 h-5 text-medical-500" />
                  <div>
                    <p className="text-sm text-gray-500">وقت الموعد</p>
                    <p className="font-semibold text-gray-800">{selectedAppointment.time}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500">تاريخ الحجز</p>
                  <p className="text-sm text-gray-600">
                    {formatDateTime(selectedAppointment.created_at)}
                  </p>
                </div>
              </div>
            </div>

            {selectedAppointment.file_names && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center space-x-2 space-x-reverse">
                  <FileText className="w-5 h-5 text-medical-500" />
                  <span>الملفات المرفقة</span>
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {selectedAppointment.file_names.split(',').map((fileName, index) => (
                    <div
                      key={index}
                      className="bg-white p-3 rounded-lg border border-gray-200 flex items-center space-x-2 space-x-reverse"
                    >
                      <FileText className="w-4 h-4 text-blue-500" />
                      <span className="text-sm text-gray-700 truncate">{fileName}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex flex-wrap gap-3 justify-center">
              {selectedAppointment.status === 'pending' && (
                <button
                  onClick={() => {
                    updateAppointmentStatus(selectedAppointment.id, 'confirmed')
                    setShowDetails(false)
                  }}
                  className="bg-green-500 text-white px-6 py-3 rounded-xl font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>تأكيد الموعد</span>
                </button>
              )}

              {selectedAppointment.status === 'confirmed' && (
                <button
                  onClick={() => {
                    updateAppointmentStatus(selectedAppointment.id, 'completed')
                    setShowDetails(false)
                  }}
                  className="bg-purple-500 text-white px-6 py-3 rounded-xl font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>إكمال الموعد</span>
                </button>
              )}

              <button
                onClick={() => window.open(`tel:${selectedAppointment.phone_number}`, '_self')}
                className="bg-indigo-500 text-white px-6 py-3 rounded-xl font-semibold btn-hover flex items-center space-x-2 space-x-reverse"
              >
                <Phone className="w-5 h-5" />
                <span>الاتصال بالمريض</span>
              </button>

              <button
                onClick={() => setShowDetails(false)}
                className="bg-gray-500 text-white px-6 py-3 rounded-xl font-semibold btn-hover"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminPanel
